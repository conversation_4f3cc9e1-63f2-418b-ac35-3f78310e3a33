import Link from "next/link"

export function Footer() {
  return (
    <footer className="bg-background border-t border-border py-16">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-8">
          <h2 className="text-6xl font-bold text-foreground mb-8">FLOAID</h2>
          <nav className="flex justify-center space-x-8">
            <Link href="/" className="text-lg text-muted-foreground hover:text-foreground transition-colors">
              Home
            </Link>
            <Link href="/catalog" className="text-lg text-muted-foreground hover:text-foreground transition-colors">
              Catalog
            </Link>
            <Link href="/product" className="text-lg text-muted-foreground hover:text-foreground transition-colors">
              Product
            </Link>
            <Link href="/cart" className="text-lg text-muted-foreground hover:text-foreground transition-colors">
              Cart
            </Link>
            <Link href="/contact" className="text-lg text-muted-foreground hover:text-foreground transition-colors">
              Contact
            </Link>
          </nav>
        </div>
      </div>
    </footer>
  )
}
