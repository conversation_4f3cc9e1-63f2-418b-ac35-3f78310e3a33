"use client"

import { Navigation } from "@/components/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { useCart } from "@/hooks/use-cart"
import { Minus, Plus, Trash2 } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"

export default function CartPage() {
  const { items, updateQuantity, removeItem, total, clearCart } = useCart()
  const router = useRouter()

  const handleCheckout = () => {
    // Navigate to thank you page and clear cart
    clearCart()
    router.push("/thank-you")
  }

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />

        <div className="max-w-4xl mx-auto px-4 py-12 text-center">
          <h1 className="text-3xl font-bold text-foreground mb-6">Your Cart is Empty</h1>
          <p className="text-muted-foreground mb-8">Add our hearing aid to your cart to get started.</p>
          <Link href="/catalog">
            <Button size="lg">Shop Now</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <div className="max-w-4xl mx-auto px-4 py-12">
        <h1 className="text-3xl font-bold text-foreground mb-8">Shopping Cart</h1>

        <div className="space-y-6">
          {items.map((item) => (
            <div key={item.id} className="border-b border-border pb-6">
              <div className="flex items-center gap-6">
                <Image
                  src={item.image || "/placeholder.svg"}
                  alt={item.name}
                  width={100}
                  height={100}
                  className="rounded-lg"
                />

                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-foreground">{item.name}</h3>
                  <p className="text-primary font-semibold">${item.price}</p>
                </div>

                <div className="flex items-center gap-3">
                  <Button variant="outline" size="sm" onClick={() => updateQuantity(item.id, item.quantity - 1)}>
                    <Minus className="h-4 w-4" />
                  </Button>
                  <span className="w-8 text-center">{item.quantity}</span>
                  <Button variant="outline" size="sm" onClick={() => updateQuantity(item.id, item.quantity + 1)}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>

                <Button variant="outline" size="sm" onClick={() => removeItem(item.id)}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 pt-6 border-t border-border">
          <div className="flex justify-between items-center mb-6">
            <span className="text-xl font-semibold">Total:</span>
            <span className="text-2xl font-bold text-primary">${total.toFixed(2)}</span>
          </div>

          <div className="space-y-4">
            <Button size="lg" className="w-full" onClick={handleCheckout}>
              Proceed to Checkout
            </Button>
            <Link href="/catalog" className="block">
              <Button variant="outline" size="lg" className="w-full bg-transparent">
                Continue Shopping
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
