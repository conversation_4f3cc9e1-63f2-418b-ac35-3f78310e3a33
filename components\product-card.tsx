"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useCart } from "@/hooks/use-cart"
import { useRouter } from "next/navigation"
import Image from "next/image"
import Link from "next/link"

interface Product {
  id: string
  name: string
  price: number
  image: string
  description: string
}

interface ProductCardProps {
  product: Product
}

export function ProductCard({ product }: ProductCardProps) {
  const { addItem } = useCart()
  const router = useRouter()

  const handleAddToCart = () => {
    addItem(product)
    router.push("/cart")
  }

  return (
    <div className="max-w-sm">
      <Link href="/product">
        <Image
          src={product.image || "/placeholder.svg"}
          alt={product.name}
          width={300}
          height={300}
          className="rounded-lg mb-4 cursor-pointer hover:opacity-90 transition-opacity"
        />
      </Link>

      <h3 className="text-xl font-semibold text-foreground mb-2">{product.name}</h3>

      <p className="text-muted-foreground mb-4">{product.description}</p>

      <p className="text-2xl font-bold text-primary mb-4">${product.price}</p>

      <div className="space-y-2">
        <Button className="w-full" onClick={handleAddToCart}>
          Add to Cart
        </Button>
        <Link href="/product">
          <Button variant="outline" className="w-full bg-transparent">
            View Details
          </Button>
        </Link>
      </div>
    </div>
  )
}
