"use client"

import { Navigation } from "@/components/navigation"
import { Button } from "@/components/ui/button"
import { useCart } from "@/hooks/use-cart"
import { useRouter } from "next/navigation"
import Image from "next/image"

const product = {
  id: "hearing-aid-1",
  name: "Behind-the-Ear Digital Hearing Aid",
  price: 299,
  image: "/placeholder.svg?height=500&width=500",
  description: "Designed for All-Day Wear",
}

export default function ProductPage() {
  const { addItem } = useCart()
  const router = useRouter()

  const handleAddToCart = () => {
    addItem(product)
    router.push("/cart")
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <div className="max-w-6xl mx-auto px-4 py-12">
        <div className="grid md:grid-cols-2 gap-12">
          {/* Product Image */}
          <div className="relative">
            <Image
              src={product.image || "/placeholder.svg"}
              alt={product.name}
              width={500}
              height={500}
              className="rounded-lg shadow-lg w-full"
            />
          </div>

          {/* Product Details */}
          <div className="space-y-6">
            <div>
              <h1 className="text-4xl font-bold text-foreground mb-2">{product.name}</h1>
              <p className="text-xl text-muted-foreground mb-4">{product.description}</p>
              <p className="text-3xl font-bold text-primary">${product.price}</p>
            </div>

            <div className="space-y-4">
              <h3 className="text-xl font-semibold">Product Features</h3>
              <ul className="space-y-3">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <span>
                    A behind-the-ear hearing aid fits comfortably behind your ear, using a discreet tube to deliver
                    amplified sound directly into your ear canal.
                  </span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <span>
                    It's sleek, lightweight, and nearly invisible — perfect for all-day use without discomfort.
                  </span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <span>
                    Whether you're in a quiet room or a busy street, enjoy clear, natural sound that helps you stay
                    connected to the world around you.
                  </span>
                </li>
              </ul>
            </div>

            <div className="bg-muted/30 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3">Why Choose This Hearing Aid?</h3>
              <p className="text-muted-foreground">
                Today, it's not just a device. It's clarity. It's comfort. It's a chance to hear the world again.
                Discreet. Life-changing. When it's gone, it's gone.
              </p>
            </div>

            <Button size="lg" className="w-full text-lg py-6" onClick={handleAddToCart}>
              Add to Cart - ${product.price}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
