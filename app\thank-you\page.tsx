import { Navigation } from "@/components/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { CheckCircle } from "lucide-react"
import Link from "next/link"

export default function ThankYouPage() {
  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <div className="max-w-2xl mx-auto px-4 py-12 text-center">
        <Card>
          <CardContent className="p-12">
            <CheckCircle className="h-16 w-16 text-primary mx-auto mb-6" />

            <h1 className="text-3xl font-bold text-foreground mb-4">Thank You for Your Order!</h1>

            <p className="text-lg text-muted-foreground mb-6">
              Your Behind-the-Ear Digital Hearing Aid is on its way. You'll receive a confirmation email shortly with
              tracking information.
            </p>

            <div className="bg-card p-6 rounded-lg mb-8">
              <h3 className="text-lg font-semibold mb-3">What's Next?</h3>
              <ul className="text-left space-y-2 text-muted-foreground">
                <li>You'll receive an order confirmation email within 24 hours</li>
                <li>Your hearing aid will be shipped within 2-3 business days</li>
                <li>Delivery typically takes 5-7 business days</li>
                <li>Full setup instructions are included in the package</li>
              </ul>
            </div>

            <p className="text-muted-foreground mb-8">
              At Floaid, we believe in focus. Thank you for choosing our carefully crafted hearing aid. It's not just a
              device - it's your chance to hear the world again.
            </p>

            <Link href="/">
              <Button className="font-red-hat-display" size="lg">Return to Home</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
