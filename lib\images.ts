export interface ImageConfig {
  src: string
  alt: string
  width: number
  height: number
  category: 'product' | 'lifestyle' | 'hero'
}

export const IMAGES = {
  // Hero and lifestyle images
  hero: {
    background: {
      src: '/images/lifestyle/elderly-lifestyle.jpg',
      alt: 'Smiling elderly couple walking together outdoors, demonstrating active lifestyle and social connection enabled by modern hearing aid technology',
      width: 1200,
      height: 800,
      category: 'hero' as const
    }
  },

  // Product images
  products: {
    hearingAidBTE: {
      main: {
        src: '/images/products/hearing-aid-bte-main.jpg',
        alt: 'Close-up view of a sleek behind-the-ear digital hearing aid featuring modern design, comfortable fit, and advanced sound processing technology',
        width: 500,
        height: 500,
        category: 'product' as const
      },
      catalog: {
        src: '/images/products/hearing-aid-product-card.jpg',
        alt: 'Professional product photo of behind-the-ear digital hearing aid showcasing its discreet design and premium build quality',
        width: 300,
        height: 300,
        category: 'product' as const
      },
      thumbnail: {
        src: '/images/products/hearing-aid-cart-thumbnail.jpg',
        alt: 'Small product image of behind-the-ear digital hearing aid for shopping cart display',
        width: 100,
        height: 100,
        category: 'product' as const
      }
    }
  },

  // Fallback images
  fallback: {
    product: {
      src: '/images/products/hearing-aid-product-card.jpg',
      alt: 'Default hearing aid product image showing behind-the-ear digital hearing aid',
      width: 300,
      height: 300,
      category: 'product' as const
    }
  }
} as const

// Helper functions
export function getProductImage(size: 'main' | 'catalog' | 'thumbnail' = 'catalog'): ImageConfig {
  return IMAGES.products.hearingAidBTE[size]
}

export function getHeroImage(): ImageConfig {
  return IMAGES.hero.background
}

export function getFallbackImage(): ImageConfig {
  return IMAGES.fallback.product
}

// Image optimization utilities
export function getOptimizedImageUrl(src: string, width?: number, height?: number, quality: number = 80): string {
  // For future implementation with image optimization service
  // Currently returns the original src
  return src
}

// Image loading priorities for performance
export const IMAGE_PRIORITIES = {
  hero: true, // Above the fold
  product: false, // Below the fold
  thumbnail: false, // Small images
} as const

// Responsive image sizes for different breakpoints
export const RESPONSIVE_SIZES = {
  hero: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  product: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  catalog: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  thumbnail: '100px',
} as const

// Get optimized image props for Next.js Image component
export function getOptimizedImageProps(imageConfig: ImageConfig, priority?: boolean) {
  return {
    src: imageConfig.src,
    alt: imageConfig.alt,
    width: imageConfig.width,
    height: imageConfig.height,
    priority: priority ?? IMAGE_PRIORITIES[imageConfig.category],
    sizes: RESPONSIVE_SIZES[imageConfig.category === 'hero' ? 'hero' : 'product'],
    quality: 85,
    placeholder: 'blur' as const,
    blurDataURL: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
  }
}
