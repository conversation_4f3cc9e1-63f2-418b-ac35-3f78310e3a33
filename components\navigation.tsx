"use client"

import Link from "next/link"
import { ShoppingCart } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useCart } from "@/hooks/use-cart"

export function Navigation() {
  const { items } = useCart()
  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0)

  return (
    <nav className="border-b bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Link href="/" className="text-2xl font-red-hat-display font-bold text-foreground">
            Floaid
          </Link>

          <div className="flex items-center space-x-8">
            <Link href="/" className="text-foreground hover:text-gray-600 transition-colors font-red-hat-display">
              Home
            </Link>
            <Link
              href="/catalog"
              className="text-foreground hover:text-gray-600 transition-colors font-red-hat-display"
            >
              Catalog
            </Link>
            <Link
              href="/contact"
              className="text-foreground hover:text-gray-600 transition-colors font-red-hat-display"
            >
              Contact
            </Link>
            <Link href="/cart" className="relative">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2 bg-transparent font-red-hat-display"
              >
                <ShoppingCart className="h-4 w-4" />
                Cart
                {itemCount > 0 && (
                  <span className="bg-foreground text-background rounded-full px-2 py-1 text-xs">{itemCount}</span>
                )}
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </nav>
  )
}
