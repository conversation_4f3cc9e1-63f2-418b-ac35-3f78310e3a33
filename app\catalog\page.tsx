"use client"

import { Navigation } from "@/components/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useCart } from "@/hooks/use-cart"
import { useRouter } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { useState } from "react"

const product = {
  id: "hearing-aid-1",
  name: "Behind-the-Ear Digital Hearing Aid",
  price: 299,
  image: "/placeholder.svg?height=300&width=300",
  description: "Designed for All-Day Wear",
}

export default function CatalogPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const { addItem } = useCart()
  const router = useRouter()

  const handleAddToCart = () => {
    addItem(product)
    router.push("/cart")
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <div className="max-w-6xl mx-auto px-4 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-foreground mb-8">Our Store</h1>
          <div className="max-w-md mx-auto mb-8">
            <Input
              type="text"
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="group relative">
            <Link href="/product">
              <div className="relative overflow-hidden rounded-lg">
                <Image
                  src={product.image || "/placeholder.svg"}
                  alt={product.name}
                  width={300}
                  height={300}
                  className="w-full h-64 object-cover cursor-pointer hover:opacity-90 transition-opacity"
                />
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    onClick={(e) => {
                      e.preventDefault()
                      handleAddToCart()
                    }}
                    className="rounded-full px-6"
                  >
                    Add to Cart
                  </Button>
                </div>
              </div>
            </Link>

            <div className="mt-4 text-center">
              <h3 className="text-lg font-semibold text-foreground mb-1">{product.name}</h3>
              <p className="text-muted-foreground mb-2">{product.description}</p>
              <p className="text-xl font-bold text-primary">${product.price}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
