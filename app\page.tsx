import { Navigation } from "@/components/navigation"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <section className="relative h-screen flex items-center justify-center">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: "url('/placeholder.svg?height=800&width=1200')",
          }}
        >
          <div className="absolute inset-0 bg-black/40"></div>
        </div>
        <div className="relative z-10 text-center text-white">
          <h1 className="text-6xl font-light italic">Don't Just Hear. Experience!</h1>
        </div>
      </section>

      <section className="py-16 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <div className="space-y-6 text-lg leading-relaxed">
            <p>
              At Floaid, we believe in focus.
              <br />
              One product at a time, chosen with care,
              <br />
              marketed with heart, sold until the very last piece finds its home.
            </p>
            <p>
              Today, it's not just a device.
              <br />
              It's clarity. It's comfort. It's a chance to hear the world again.
            </p>
            <p>
              The Behind-the-Ear Digital Hearing Aid.
              <br />
              Discreet. Life-changing. When it's gone, it's gone.
              <br />
              Floaid brings what matters, one product at a time.
            </p>
          </div>
        </div>
      </section>

      <section className="py-16 px-4 bg-card">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="flex flex-col justify-between h-full">
              <div>
                <h3 className="text-sm font-semibold text-muted-foreground mb-4 tracking-wider uppercase">
                  HEARING AID
                </h3>
                <div className="space-y-4 text-lg">
                  <p>Designed for All-Day Wear</p>
                  <p>
                    A behind-the-ear hearing aid fits comfortably behind your ear, using a discreet tube to deliver
                    amplified sound directly into your ear canal. It's sleek, lightweight, and nearly invisible —
                    perfect for all-day use without discomfort.
                  </p>
                  <p>
                    Whether you're in a quiet room or a busy street, enjoy clear, natural sound that helps you stay
                    connected to the world around you.
                  </p>
                </div>
              </div>
              <div className="mt-8">
                <Link href="/catalog">
                  <Button size="lg" className="px-8">
                    shop hearing aids
                  </Button>
                </Link>
              </div>
            </div>
            <div className="relative">
              <Image
                src="/placeholder.svg?height=500&width=500"
                alt="Behind-the-ear digital hearing aid"
                width={500}
                height={500}
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
