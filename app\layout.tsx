import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Newsreader, Red_Hat_Display } from "next/font/google"
import { CartProvider } from "@/hooks/use-cart"
import { Footer } from "@/components/footer"
import "./globals.css"

const newsreader = Newsreader({
  subsets: ["latin"],
  weight: "300",
  style: "italic",
  display: "swap",
  variable: "--font-newsreader",
})

const redHatDisplay = Red_Hat_Display({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  display: "swap",
  variable: "--font-red-hat-display",
})

export const metadata: Metadata = {
  title: "Floaid - Behind-the-Ear Digital Hearing Aid",
  description:
    "Don't Just Hear. Experience! Premium behind-the-ear digital hearing aids designed for all-day comfort and clarity.",
  generator: "v0.app",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className={`${newsreader.variable} ${redHatDisplay.variable}`}>
      <body className="font-newsreader">
        <CartProvider>
          {children}
          <Footer />
        </CartProvider>
      </body>
    </html>
  )
}
